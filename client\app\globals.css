@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.75rem;

  /* Nerdy Style - Light Mode (Fallback) */
  --background: oklch(0.98 0.002 240);
  --foreground: oklch(0.09 0.005 240);
  --card: oklch(0.99 0.001 240);
  --card-foreground: oklch(0.09 0.005 240);
  --popover: oklch(0.99 0.001 240);
  --popover-foreground: oklch(0.09 0.005 240);

  /* Tech-inspired Primary Colors */
  --primary: oklch(0.65 0.25 260); /* Electric Blue */
  --primary-foreground: oklch(0.98 0.002 240);
  --secondary: oklch(0.15 0.02 240); /* Dark Slate */
  --secondary-foreground: oklch(0.85 0.01 240);

  /* Nerdy Accent Colors */
  --accent: oklch(0.75 0.25 140); /* Neon Green */
  --accent-foreground: oklch(0.09 0.005 240);
  --muted: oklch(0.94 0.005 240);
  --muted-foreground: oklch(0.45 0.01 240);

  /* System Colors */
  --destructive: oklch(0.65 0.25 15); /* Neon Red */
  --destructive-foreground: oklch(0.98 0.002 240);
  --border: oklch(0.88 0.005 240);
  --input: oklch(0.92 0.005 240);
  --ring: oklch(0.65 0.25 260);

  /* Chart Colors - Tech Palette */
  --chart-1: oklch(0.65 0.25 260); /* Electric Blue */
  --chart-2: oklch(0.75 0.25 140); /* Neon Green */
  --chart-3: oklch(0.70 0.25 300); /* Electric Purple */
  --chart-4: oklch(0.75 0.25 60);  /* Neon Yellow */
  --chart-5: oklch(0.65 0.25 15);  /* Neon Red */

  /* Sidebar */
  --sidebar: oklch(0.96 0.002 240);
  --sidebar-foreground: oklch(0.15 0.02 240);
  --sidebar-primary: oklch(0.65 0.25 260);
  --sidebar-primary-foreground: oklch(0.98 0.002 240);
  --sidebar-accent: oklch(0.92 0.005 240);
  --sidebar-accent-foreground: oklch(0.15 0.02 240);
  --sidebar-border: oklch(0.88 0.005 240);
  --sidebar-ring: oklch(0.65 0.25 260);

  /* Custom Nerdy Kost Theme Variables */
  --kost-primary: oklch(0.65 0.25 260);    /* Electric Blue */
  --kost-secondary: oklch(0.75 0.25 140);  /* Neon Green */
  --kost-accent: oklch(0.70 0.25 300);     /* Electric Purple */
  --kost-success: oklch(0.75 0.25 140);    /* Neon Green */
  --kost-warning: oklch(0.75 0.25 60);     /* Neon Yellow */
  --kost-error: oklch(0.65 0.25 15);       /* Neon Red */
  --kost-info: oklch(0.65 0.25 260);       /* Electric Blue */

  /* Glassmorphism Variables */
  --glass-bg: oklch(0.99 0.001 240 / 0.8);
  --glass-border: oklch(0.85 0.01 240 / 0.2);
  --glass-shadow: oklch(0.15 0.02 240 / 0.1);

  /* Neon Glow Effects */
  --neon-glow-primary: oklch(0.65 0.25 260 / 0.5);
  --neon-glow-secondary: oklch(0.75 0.25 140 / 0.5);
  --neon-glow-accent: oklch(0.70 0.25 300 / 0.5);
}

.dark {
  /* Nerdy Style - Dark Mode (Primary) */
  --background: oklch(0.08 0.005 240);      /* Deep Dark Blue */
  --foreground: oklch(0.92 0.01 240);       /* Light Gray-Blue */
  --card: oklch(0.12 0.01 240);             /* Dark Card */
  --card-foreground: oklch(0.92 0.01 240);
  --popover: oklch(0.10 0.008 240);
  --popover-foreground: oklch(0.92 0.01 240);

  /* Tech-inspired Primary Colors - Dark Mode */
  --primary: oklch(0.75 0.25 260);          /* Brighter Electric Blue */
  --primary-foreground: oklch(0.08 0.005 240);
  --secondary: oklch(0.18 0.02 240);        /* Lighter Dark Slate */
  --secondary-foreground: oklch(0.85 0.01 240);

  /* Nerdy Accent Colors - Dark Mode */
  --accent: oklch(0.80 0.25 140);           /* Brighter Neon Green */
  --accent-foreground: oklch(0.08 0.005 240);
  --muted: oklch(0.16 0.01 240);
  --muted-foreground: oklch(0.65 0.01 240);

  /* System Colors - Dark Mode */
  --destructive: oklch(0.70 0.25 15);       /* Brighter Neon Red */
  --destructive-foreground: oklch(0.92 0.01 240);
  --border: oklch(0.22 0.01 240);
  --input: oklch(0.18 0.01 240);
  --ring: oklch(0.75 0.25 260);

  /* Chart Colors - Dark Mode Tech Palette */
  --chart-1: oklch(0.75 0.25 260);          /* Electric Blue */
  --chart-2: oklch(0.80 0.25 140);          /* Neon Green */
  --chart-3: oklch(0.75 0.25 300);          /* Electric Purple */
  --chart-4: oklch(0.80 0.25 60);           /* Neon Yellow */
  --chart-5: oklch(0.70 0.25 15);           /* Neon Red */

  /* Sidebar - Dark Mode */
  --sidebar: oklch(0.10 0.008 240);
  --sidebar-foreground: oklch(0.85 0.01 240);
  --sidebar-primary: oklch(0.75 0.25 260);
  --sidebar-primary-foreground: oklch(0.08 0.005 240);
  --sidebar-accent: oklch(0.16 0.01 240);
  --sidebar-accent-foreground: oklch(0.85 0.01 240);
  --sidebar-border: oklch(0.22 0.01 240);
  --sidebar-ring: oklch(0.75 0.25 260);

  /* Custom Nerdy Kost Theme Variables - Dark Mode */
  --kost-primary: oklch(0.75 0.25 260);     /* Brighter Electric Blue */
  --kost-secondary: oklch(0.80 0.25 140);   /* Brighter Neon Green */
  --kost-accent: oklch(0.75 0.25 300);      /* Brighter Electric Purple */
  --kost-success: oklch(0.80 0.25 140);     /* Brighter Neon Green */
  --kost-warning: oklch(0.80 0.25 60);      /* Brighter Neon Yellow */
  --kost-error: oklch(0.70 0.25 15);        /* Brighter Neon Red */
  --kost-info: oklch(0.75 0.25 260);        /* Brighter Electric Blue */

  /* Glassmorphism Variables - Dark Mode */
  --glass-bg: oklch(0.12 0.01 240 / 0.8);
  --glass-border: oklch(0.35 0.02 240 / 0.3);
  --glass-shadow: oklch(0.05 0.005 240 / 0.3);

  /* Neon Glow Effects - Dark Mode */
  --neon-glow-primary: oklch(0.75 0.25 260 / 0.6);
  --neon-glow-secondary: oklch(0.80 0.25 140 / 0.6);
  --neon-glow-accent: oklch(0.75 0.25 300 / 0.6);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  /* Nerdy Style - Enhanced Kost Card */
  .kost-card {
    @apply transition-all duration-500 ease-out;
    @apply hover:shadow-2xl hover:-translate-y-2;
    @apply border border-border/50;
    @apply bg-gradient-to-br from-card to-card/80;
    @apply backdrop-blur-sm;
  }

  .kost-card:hover {
    @apply border-primary/30;
    box-shadow: 0 25px 50px -12px var(--neon-glow-primary);
  }

  .kost-card-image {
    @apply transition-all duration-500 hover:scale-110;
    @apply rounded-lg overflow-hidden;
  }

  /* Glassmorphism Card */
  .glass-card {
    @apply bg-card/80 border border-border/20 shadow-2xl;
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
  }

  /* Neon Glow Effects */
  .neon-glow-primary {
    box-shadow: 0 0 20px oklch(0.65 0.25 260 / 0.5);
  }

  .neon-glow-secondary {
    box-shadow: 0 0 20px oklch(0.75 0.25 140 / 0.5);
  }

  .neon-glow-accent {
    box-shadow: 0 0 20px oklch(0.70 0.25 300 / 0.5);
  }

  /* Enhanced Search Bar */
  .search-bar {
    @apply relative flex items-center w-full max-w-4xl mx-auto;
  }

  .search-input-glass {
    @apply bg-card/80 border-0 backdrop-blur-sm;
    @apply focus:shadow-lg focus:shadow-primary/20;
    @apply transition-all duration-300;
  }

  /* Filter Panel - Nerdy Style */
  .filter-panel {
    @apply space-y-6 p-6 bg-card/80 rounded-xl backdrop-blur-sm;
    @apply border border-border/30;
  }

  /* Price Range Styling */
  .price-range {
    @apply flex items-center justify-between text-sm text-muted-foreground;
    @apply font-mono; /* Code-inspired font */
  }

  /* Comparison Table - Enhanced */
  .comparison-table {
    @apply w-full border-collapse bg-card/80 rounded-xl overflow-hidden backdrop-blur-sm;
    @apply border border-border/30;
  }

  /* Hero Section - Nerdy Gradient */
  .hero-gradient {
    background: linear-gradient(135deg,
      var(--kost-primary) 0%,
      var(--kost-accent) 50%,
      var(--kost-secondary) 100%);
  }

  .hero-pattern {
    background-image:
      radial-gradient(circle at 25% 25%, var(--neon-glow-primary) 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, var(--neon-glow-secondary) 0%, transparent 50%);
  }

  /* Responsive Bento Box Grid */
  .bento-grid {
    display: grid;
    gap: 1rem;
    grid-auto-rows: minmax(280px, auto);
  }

  /* Mobile First Approach */
  @media (max-width: 768px) {
    .bento-grid {
      grid-template-columns: 1fr;
      gap: 1rem;
    }

    .bento-item-large,
    .bento-item-wide {
      grid-row: span 1;
      grid-column: span 1;
    }
  }

  /* Tablet */
  @media (min-width: 769px) and (max-width: 1024px) {
    .bento-grid {
      grid-template-columns: repeat(2, 1fr);
      gap: 1.25rem;
    }

    .bento-item-large {
      grid-row: span 2;
    }

    .bento-item-wide {
      grid-column: span 2;
    }
  }

  /* Desktop */
  @media (min-width: 1025px) {
    .bento-grid {
      grid-template-columns: repeat(3, 1fr);
      gap: 1.5rem;
    }

    .bento-item-large {
      grid-row: span 2;
    }

    .bento-item-wide {
      grid-column: span 2;
    }
  }

  /* Large Desktop */
  @media (min-width: 1440px) {
    .bento-grid {
      grid-template-columns: repeat(4, 1fr);
      gap: 2rem;
    }
  }

  /* Floating Elements */
  .floating-element {
    @apply animate-pulse;
    animation: float 6s ease-in-out infinite;
  }

  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }

  /* Advanced Typography System */
  .code-text {
    @apply font-mono text-sm;
    @apply bg-muted/50 px-2 py-1 rounded;
    @apply border border-border/30;
  }

  .heading-gradient {
    @apply text-transparent bg-clip-text;
    background-image: linear-gradient(135deg, var(--primary), var(--accent));
  }

  .text-glow {
    text-shadow: 0 0 20px var(--neon-glow-primary);
  }

  .text-shadow-soft {
    text-shadow: 0 2px 4px var(--glass-shadow);
  }

  /* Typography Hierarchy */
  .display-1 {
    @apply text-6xl md:text-7xl lg:text-8xl font-bold leading-none tracking-tight;
  }

  .display-2 {
    @apply text-5xl md:text-6xl lg:text-7xl font-bold leading-tight tracking-tight;
  }

  .display-3 {
    @apply text-4xl md:text-5xl lg:text-6xl font-bold leading-tight;
  }

  .heading-1 {
    @apply text-3xl md:text-4xl lg:text-5xl font-bold leading-tight;
  }

  .heading-2 {
    @apply text-2xl md:text-3xl lg:text-4xl font-semibold leading-tight;
  }

  .heading-3 {
    @apply text-xl md:text-2xl lg:text-3xl font-semibold leading-snug;
  }

  .body-large {
    @apply text-lg md:text-xl leading-relaxed;
  }

  .body-medium {
    @apply text-base md:text-lg leading-relaxed;
  }

  .caption {
    @apply text-sm text-muted-foreground font-mono;
  }

  .label {
    @apply text-xs font-medium uppercase tracking-wider;
  }

  /* Custom Scrollbar - Enhanced */
  .custom-scrollbar::-webkit-scrollbar {
    width: 8px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    @apply bg-muted/30 rounded-full;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    @apply bg-primary/60 rounded-full;
    @apply hover:bg-primary/80;
    @apply transition-colors duration-200;
  }

  /* Hover Glow Animation */
  .hover-glow {
    @apply transition-all duration-300;
  }

  .hover-glow:hover {
    @apply neon-glow-primary;
  }

  /* Responsive Utilities */
  @media (max-width: 768px) {
    .glass-card {
      @apply bg-card/90;
      backdrop-filter: blur(8px);
      -webkit-backdrop-filter: blur(8px);
    }

    .floating-element {
      animation: none; /* Disable animations on mobile for performance */
    }

    .neon-glow-primary,
    .neon-glow-secondary,
    .neon-glow-accent {
      box-shadow: none; /* Reduce glow effects on mobile */
    }

    .display-1 {
      @apply text-4xl md:text-5xl;
    }

    .display-2 {
      @apply text-3xl md:text-4xl;
    }

    .display-3 {
      @apply text-2xl md:text-3xl;
    }
  }

  /* Tablet Optimizations */
  @media (min-width: 769px) and (max-width: 1024px) {
    .glass-card {
      @apply bg-card/85;
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
    }
  }

  /* Desktop Enhancements */
  @media (min-width: 1025px) {
    .glass-card {
      @apply bg-card/80;
      backdrop-filter: blur(12px);
      -webkit-backdrop-filter: blur(12px);
    }

    .floating-element {
      animation: float 6s ease-in-out infinite;
    }

    .hover-glow:hover {
      @apply neon-glow-primary;
    }
  }

  /* Reduce motion for accessibility */
  @media (prefers-reduced-motion: reduce) {
    .floating-element,
    .kost-card,
    .kost-card-image {
      animation: none !important;
      transition: none !important;
    }
  }
}
