"use client"

import Image from "next/image"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON>Footer, CardHeader } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { 
  MapPin, 
  Wifi, 
  Car, 
  Utensils, 
  Zap, 
  Droplets, 
  Shield, 
  Users,
  Heart,
  Eye,
  Star
} from "lucide-react"
import { cn } from "@/lib/utils"
import { formatPrice } from "@/lib/format"
import { DEFAULT_KOST_IMAGE, getSafeImageUrl } from "@/lib/images"

export interface KostData {
  id: string
  title: string
  location: string
  price: number
  rating: number
  reviewCount: number
  images: string[]
  facilities: string[]
  type: "putra" | "putri" | "campur"
  available: number
  description: string
  isWishlisted?: boolean
}

interface KostCardProps {
  kost: KostData
  onPreview?: (kost: KostData) => void
  onWishlist?: (kostId: string) => void
  onCompare?: (kostId: string) => void
  isComparing?: boolean
  className?: string
}

const facilityIcons: Record<string, React.ComponentType<any>> = {
  wifi: Wifi,
  parkir: Car,
  dapur: Utensils,
  listrik: Zap,
  air: Droplets,
  keamanan: Shield,
  "ruang tamu": Users,
}

export function KostCard({ 
  kost, 
  onPreview, 
  onWishlist, 
  onCompare,
  isComparing = false,
  className 
}: KostCardProps) {


  const getTypeColor = (type: string) => {
    switch (type) {
      case "putra":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300"
      case "putri":
        return "bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-300"
      case "campur":
        return "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300"
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300"
    }
  }

  return (
    <Card className={cn("kost-card group overflow-hidden", className)}>
      <CardHeader className="p-0 relative">
        <div className="relative aspect-[4/3] overflow-hidden rounded-t-xl">
          <Image
            src={getSafeImageUrl(kost.images[0], 'kost') || DEFAULT_KOST_IMAGE}
            alt={kost.title}
            fill
            className="kost-card-image object-cover transition-all duration-700 group-hover:scale-110"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          />

          {/* Enhanced Overlay with Glassmorphism */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-all duration-500">
            {/* Floating Action Buttons */}
            <div className="absolute top-4 right-4 flex gap-2 transform translate-y-2 opacity-0 group-hover:translate-y-0 group-hover:opacity-100 transition-all duration-500 delay-100">
              <Button
                size="sm"
                variant="secondary"
                className="h-9 w-9 p-0 bg-white/10 border-white/20 hover:shadow-lg hover:shadow-accent/30 backdrop-blur-md"
                onClick={() => onWishlist?.(kost.id)}
              >
                <Heart
                  className={cn(
                    "h-4 w-4 transition-all duration-300",
                    kost.isWishlisted
                      ? "fill-red-400 text-red-400 scale-110"
                      : "text-white hover:text-red-400"
                  )}
                />
              </Button>
              <Button
                size="sm"
                variant="secondary"
                className="h-9 w-9 p-0 bg-white/10 border-white/20 hover:shadow-lg hover:shadow-primary/30 backdrop-blur-md"
                onClick={() => onPreview?.(kost)}
              >
                <Eye className="h-4 w-4 text-white hover:text-primary transition-colors duration-300" />
              </Button>
            </div>

            {/* Quick Preview Info */}
            <div className="absolute bottom-4 left-4 right-4 transform translate-y-2 opacity-0 group-hover:translate-y-0 group-hover:opacity-100 transition-all duration-500 delay-200">
              <div className="bg-white/10 backdrop-blur-sm p-3 rounded-lg border border-white/20">
                <div className="flex items-center justify-between text-white">
                  <div className="flex items-center gap-2">
                    <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                    <span className="font-medium text-sm">{kost.rating}</span>
                  </div>
                  <div className="code-text bg-white/10 text-white border-white/20 text-xs">
                    {kost.available} available
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          {/* Enhanced Type Badge */}
          <div className="absolute top-4 left-4">
            <Badge className={cn(
              "bg-white/10 border-white/20 backdrop-blur-md font-mono text-xs",
              getTypeColor(kost.type)
            )}>
              <span className="code-text bg-transparent border-0 p-0">
                {kost.type.toUpperCase()}
              </span>
            </Badge>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="p-6">
        <div className="space-y-4">
          {/* Enhanced Title and Location */}
          <div className="space-y-2">
            <h3 className="font-bold text-xl line-clamp-1 group-hover:text-primary transition-all duration-300 group-hover:translate-x-1">
              {kost.title}
            </h3>
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <div className="flex items-center gap-1 bg-card/80 backdrop-blur-sm px-2 py-1 rounded-md border border-border/30">
                <MapPin className="h-3 w-3 text-primary" />
                <span className="line-clamp-1 font-mono text-xs">{kost.location}</span>
              </div>
            </div>
          </div>

          {/* Enhanced Rating with Animation */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="flex items-center gap-1 bg-card/80 backdrop-blur-sm px-3 py-1 rounded-full border border-yellow-400/30">
                <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                <span className="font-bold text-sm font-mono">{kost.rating}</span>
              </div>
              <span className="text-xs text-muted-foreground">
                ({kost.reviewCount} reviews)
              </span>
            </div>

            {/* Live Status Indicator */}
            <div className="flex items-center gap-1">
              <div className="w-2 h-2 bg-accent rounded-full animate-pulse shadow-lg shadow-accent/50"></div>
              <span className="text-xs text-muted-foreground font-mono">LIVE</span>
            </div>
          </div>

          {/* Enhanced Facilities */}
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <span className="text-xs text-muted-foreground font-mono">// facilities</span>
              <div className="flex-1 h-px bg-border/30"></div>
            </div>
            <div className="flex flex-wrap gap-2">
              {kost.facilities.slice(0, 3).map((facility, index) => {
                const IconComponent = facilityIcons[facility.toLowerCase()] || Shield
                return (
                  <div
                    key={facility}
                    className="flex items-center gap-1 text-xs bg-card/80 backdrop-blur-sm px-3 py-1.5 rounded-lg border border-border/30 hover:border-primary/30 transition-all duration-300 hover:shadow-lg hover:shadow-primary/20"
                    style={{animationDelay: `${index * 100}ms`}}
                  >
                    <IconComponent className="h-3 w-3 flex-shrink-0 text-primary" />
                    <span className="truncate font-mono">{facility}</span>
                  </div>
                )
              })}
              {kost.facilities.length > 3 && (
                <div className="text-xs bg-card/80 backdrop-blur-sm px-3 py-1.5 rounded-lg border border-border/30 font-mono text-muted-foreground">
                  +{kost.facilities.length - 3}
                </div>
              )}
            </div>
          </div>
        </div>
      </CardContent>
      
      <Separator />
      
      <Separator className="opacity-30" />

      <CardFooter className="p-6 pt-4">
        <div className="flex items-center justify-between w-full">
          {/* Enhanced Price Display */}
          <div className="space-y-1">
            <div className="flex items-baseline gap-2">
              <span className="text-3xl font-bold text-primary font-mono">
                {formatPrice(kost.price)}
              </span>
              <div className="code-text bg-primary/10 text-primary border-primary/30 text-xs">
                /month
              </div>
            </div>
            <div className="text-xs text-muted-foreground font-mono">
              // competitive pricing
            </div>
          </div>

          {/* Enhanced Action Buttons */}
          <div className="flex gap-2 flex-col sm:flex-row">
            {onCompare && (
              <Button
                variant={isComparing ? "default" : "outline"}
                size="sm"
                onClick={() => onCompare(kost.id)}
                className={cn(
                  "flex-1 sm:flex-none font-mono text-xs transition-all duration-300",
                  isComparing
                    ? "shadow-lg shadow-primary/30"
                    : "bg-card/80 backdrop-blur-sm border-border/30 hover:border-primary/50 hover:shadow-lg hover:shadow-primary/20"
                )}
              >
                {isComparing ? "SELECTED" : "COMPARE"}
              </Button>
            )}
            <Button
              size="sm"
              onClick={() => onPreview?.(kost)}
              className="flex-1 sm:flex-none font-mono text-xs shadow-lg shadow-primary/30 hover:shadow-accent/30 transition-all duration-300"
            >
              VIEW_DETAILS
            </Button>
          </div>
        </div>
      </CardFooter>
    </Card>
  )
}
