"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { SearchBar, SearchFilters } from "./search-bar"
import { 
  MapPin, 
  Star, 
  Users, 
  Shield, 
  Zap,
  TrendingUp,
  CheckCircle
} from "lucide-react"

interface HeroSectionProps {
  onSearch: (query: string, filters: SearchFilters) => void
}

const stats = [
  {
    icon: Users,
    value: "10,000+",
    label: "Pengguna Aktif"
  },
  {
    icon: MapPin,
    value: "500+",
    label: "Kost Terdaftar"
  },
  {
    icon: Star,
    value: "4.8",
    label: "Rating Rata-rata"
  },
  {
    icon: Shield,
    value: "100%",
    label: "Terverifikasi"
  }
]

const features = [
  {
    icon: Zap,
    title: "Preview Dinamis",
    description: "Lihat detail kost dengan preview interaktif dan carousel gambar"
  },
  {
    icon: TrendingUp,
    title: "Perbandingan Mudah",
    description: "Bandingkan hingga 3 kost sekaligus dengan tabel perbandingan detail"
  },
  {
    icon: CheckCircle,
    title: "Terverifikasi",
    description: "Semua kost telah diverifikasi untuk memastikan kualitas dan keamanan"
  }
]

const popularLocations = [
  "Jakarta Selatan",
  "Bandung",
  "Yogyakarta", 
  "Surabaya",
  "Malang",
  "Semarang"
]

export function HeroSection({ onSearch }: HeroSectionProps) {
  const handleLocationSearch = (location: string) => {
    const filters: SearchFilters = {
      location,
      type: "semua",
      priceRange: [500000, 5000000],
      facilities: [],
      sortBy: "relevance"
    }
    onSearch("", filters)
  }

  return (
    <section className="relative overflow-hidden min-h-screen flex items-center">
      {/* Enhanced Background */}
      <div className="absolute inset-0 hero-gradient" />
      <div className="absolute inset-0 hero-pattern opacity-30" />

      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-20 left-10 w-72 h-72 bg-primary/10 rounded-full blur-3xl floating-element"></div>
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-accent/10 rounded-full blur-3xl floating-element" style={{animationDelay: '2s'}}></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-secondary/10 rounded-full blur-3xl floating-element" style={{animationDelay: '4s'}}></div>
      </div>

      {/* Tech Grid Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='80' height='80' viewBox='0 0 80 80' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M0 0h80v80H0V0zm20 20v40h40V20H20zm20 35a15 15 0 1 1 0-30 15 15 0 0 1 0 30z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }} />
      </div>

      <div className="relative container mx-auto px-4 py-20">
        {/* Asymmetric Layout */}
        <div className="grid lg:grid-cols-12 gap-12 items-center min-h-[80vh]">
          {/* Main Content - Left Side */}
          <div className="lg:col-span-7 space-y-8">
            {/* Enhanced Main Heading */}
            <div className="space-y-6">
              <div className="flex items-center gap-3">
                <Badge variant="secondary" className="bg-white/10 border-white/30 text-white neon-glow-primary backdrop-blur-sm">
                  <span className="code-text bg-transparent border-0 p-0 text-white">KOSTHUB_v2.0</span>
                </Badge>
                <div className="floating-element">
                  <div className="w-3 h-3 bg-accent rounded-full neon-glow-secondary"></div>
                </div>
              </div>

              <h1 className="text-5xl md:text-7xl lg:text-8xl font-bold text-white leading-[0.9] tracking-tight">
                Find Your
                <br />
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-accent via-primary to-secondary">
                  Perfect
                </span>
                <br />
                <span className="text-4xl md:text-5xl lg:text-6xl text-white/80">
                  Kost
                </span>
              </h1>

              <div className="space-y-4 max-w-2xl">
                <p className="text-xl md:text-2xl text-white/90 leading-relaxed">
                  Platform inovatif dengan
                  <span className="code-text mx-2 bg-white/10 text-accent border-accent/30">AI-powered</span>
                  search dan preview dinamis
                </p>
                <p className="text-lg text-white/70">
                  Temukan tempat tinggal yang sempurna dengan teknologi terdepan
                </p>
              </div>
            </div>

            {/* Enhanced Search Bar */}
            <div className="space-y-6">
              <SearchBar
                onSearch={onSearch}
                placeholder="Search by location, name, or facilities..."
                className="bg-white/10 border-white/20 rounded-2xl p-3 shadow-2xl neon-glow-primary backdrop-blur-sm"
              />

              {/* Popular Locations - Redesigned */}
              <div className="space-y-4">
                <p className="text-white/80 text-sm font-medium font-mono">
                  // Popular locations
                </p>
                <div className="flex flex-wrap gap-3">
                  {popularLocations.slice(0, 4).map((location) => (
                    <Button
                      key={location}
                      variant="outline"
                      size="sm"
                      className="bg-white/10 backdrop-blur-sm border-white/20 text-white hover:border-accent/50 transition-all duration-300 font-mono text-xs"
                      onClick={() => handleLocationSearch(location)}
                    >
                      <MapPin className="h-3 w-3 mr-1" />
                      {location}
                    </Button>
                  ))}
                  <Button
                    variant="outline"
                    size="sm"
                    className="bg-white/10 backdrop-blur-sm border-white/20 text-white/60 hover:text-white transition-all duration-300 font-mono text-xs"
                  >
                    +{popularLocations.length - 4} more
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Right Side - Floating Elements */}
          <div className="lg:col-span-5 relative">
            {/* Main Floating Card */}
            <div className="bg-white/10 backdrop-blur-sm p-8 rounded-3xl border border-white/20 floating-element shadow-2xl">
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <h3 className="text-2xl font-bold text-white">Live Stats</h3>
                  <div className="w-3 h-3 bg-accent rounded-full animate-pulse neon-glow-secondary"></div>
                </div>

                <div className="grid grid-cols-2 gap-6">
                  {stats.map((stat, index) => (
                    <div key={index} className="text-center space-y-2">
                      <div className="inline-flex items-center justify-center w-12 h-12 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20">
                        <stat.icon className="h-6 w-6 text-accent" />
                      </div>
                      <div className="text-3xl font-bold text-white font-mono">
                        {stat.value}
                      </div>
                      <div className="text-sm text-white/70">
                        {stat.label}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Secondary Floating Elements */}
            <div className="absolute -top-6 -right-6 bg-white/10 backdrop-blur-sm p-4 rounded-2xl border border-accent/30 floating-element shadow-lg" style={{animationDelay: '1s'}}>
              <div className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-accent" />
                <span className="text-white font-mono text-sm">Verified</span>
              </div>
            </div>

            <div className="absolute -bottom-4 -left-4 bg-white/10 backdrop-blur-sm p-3 rounded-xl border border-primary/30 floating-element shadow-lg" style={{animationDelay: '3s'}}>
              <div className="flex items-center gap-2">
                <Zap className="h-4 w-4 text-primary" />
                <span className="text-white font-mono text-xs">AI-Powered</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="relative bg-white/5 backdrop-blur-sm border-t border-white/10">
        <div className="container mx-auto px-4 py-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
              Mengapa Memilih KostHub?
            </h2>
            <p className="text-white/80 text-lg max-w-2xl mx-auto">
              Fitur-fitur inovatif yang memudahkan pencarian kost impian Anda
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            {features.map((feature, index) => (
              <div key={index} className="text-center space-y-4 group">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-white/10 rounded-2xl mb-4 group-hover:bg-white/20 transition-colors duration-300">
                  <feature.icon className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-white">
                  {feature.title}
                </h3>
                <p className="text-white/80 leading-relaxed">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>

          {/* CTA */}
          <div className="text-center mt-12">
            <Button 
              size="lg" 
              className="bg-white text-primary hover:bg-white/90 font-semibold px-8 py-3 text-lg"
              onClick={() => {
                document.getElementById('kost-listings')?.scrollIntoView({ 
                  behavior: 'smooth' 
                })
              }}
            >
              Jelajahi Kost Sekarang
            </Button>
          </div>
        </div>
      </div>
    </section>
  )
}
