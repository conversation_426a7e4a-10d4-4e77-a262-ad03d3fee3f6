"use client"

import { useState, lazy, Suspense } from "react"
import { HeroSec<PERSON> } from "@/components/hero-section"
import { <PERSON>st<PERSON><PERSON>, KostData } from "@/components/kost-card"
import { DialogSkeleton } from "@/components/loading"
import { UNSPLASH_IMAGES } from "@/lib/images"
import Image from "next/image"

// Lazy load heavy components
const KostPreviewDialog = lazy(() => import("@/components/kost-preview-dialog").then(mod => ({ default: mod.KostPreviewDialog })))
const ComparisonDialog = lazy(() => import("@/components/comparison-dialog").then(mod => ({ default: mod.ComparisonDialog })))
import { SearchFilters } from "@/components/search-bar"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import {
  TrendingUp,
  Star,
  MapPin,
  Users,
  ArrowRight,
  Heart,
  Shield
} from "lucide-react"

// Mock data untuk featured kost
const featuredKosts: KostData[] = [
  {
    id: "1",
    title: "Kost Melati Residence",
    location: "Kemang, Jakarta Selatan",
    price: 2500000,
    rating: 4.8,
    reviewCount: 124,
    images: [
      UNSPLASH_IMAGES.kost.room1,
      UNSPLASH_IMAGES.kost.interior1,
      UNSPLASH_IMAGES.kost.interior2
    ],
    facilities: ["WiFi", "Parkir", "Dapur", "Listrik", "Air", "Keamanan"],
    type: "putri",
    available: 3,
    description: "Kost nyaman dan strategis di kawasan Kemang dengan fasilitas lengkap dan keamanan 24 jam.",
    isWishlisted: false
  },
  {
    id: "2",
    title: "Griya Mahasiswa Bandung",
    location: "Dago, Bandung",
    price: 1800000,
    rating: 4.6,
    reviewCount: 89,
    images: [
      UNSPLASH_IMAGES.kost.room2,
      UNSPLASH_IMAGES.kost.interior3
    ],
    facilities: ["WiFi", "Dapur", "Listrik", "Air", "Ruang Tamu"],
    type: "putra",
    available: 5,
    description: "Kost khusus mahasiswa dengan suasana yang kondusif untuk belajar dan dekat dengan kampus.",
    isWishlisted: true
  },
  {
    id: "3",
    title: "Kost Harmoni Yogya",
    location: "Malioboro, Yogyakarta",
    price: 1500000,
    rating: 4.7,
    reviewCount: 156,
    images: [UNSPLASH_IMAGES.kost.room3],
    facilities: ["WiFi", "Parkir", "Listrik", "Air", "Keamanan", "AC"],
    type: "campur",
    available: 2,
    description: "Kost dengan lokasi strategis di jantung kota Yogyakarta, dekat dengan berbagai fasilitas umum.",
    isWishlisted: false
  }
]

const testimonials = [
  {
    name: "Sarah Putri",
    location: "Jakarta",
    rating: 5,
    comment: "Platform yang sangat membantu! Fitur perbandingan kostnya sangat berguna untuk memilih kost yang tepat.",
    avatar: UNSPLASH_IMAGES.avatars.female1
  },
  {
    name: "Ahmad Rizki",
    location: "Bandung",
    rating: 5,
    comment: "Preview dinamis membuat saya bisa melihat detail kost dengan jelas sebelum memutuskan. Recommended!",
    avatar: UNSPLASH_IMAGES.avatars.male1
  },
  {
    name: "Dina Maharani",
    location: "Yogyakarta",
    rating: 4,
    comment: "Pencarian kost jadi lebih mudah dan cepat. Interface yang user-friendly dan informatif.",
    avatar: UNSPLASH_IMAGES.avatars.female2
  }
]

export default function Home() {
  const [selectedKost, setSelectedKost] = useState<KostData | null>(null)
  const [isPreviewOpen, setIsPreviewOpen] = useState(false)
  const [comparisonKosts, setComparisonKosts] = useState<KostData[]>([])
  const [isComparisonOpen, setIsComparisonOpen] = useState(false)
  const [wishlistedKosts, setWishlistedKosts] = useState<string[]>(["2"])

  const handleSearch = (query: string, filters: SearchFilters) => {
    // TODO: Implement search functionality
    console.log("Search:", query, filters)
    // Navigate to listings page or filter results
  }

  const handlePreview = (kost: KostData) => {
    setSelectedKost(kost)
    setIsPreviewOpen(true)
  }

  const handleWishlist = (kostId: string) => {
    setWishlistedKosts(prev =>
      prev.includes(kostId)
        ? prev.filter(id => id !== kostId)
        : [...prev, kostId]
    )
  }

  const handleCompare = (kostId: string) => {
    const kost = featuredKosts.find(k => k.id === kostId)
    if (!kost) return

    setComparisonKosts(prev => {
      const isAlreadyComparing = prev.some(k => k.id === kostId)
      if (isAlreadyComparing) {
        return prev.filter(k => k.id !== kostId)
      } else if (prev.length < 3) {
        return [...prev, kost]
      } else {
        // Replace the first item if already at max
        return [kost, ...prev.slice(1)]
      }
    })
  }

  const removeFromComparison = (kostId: string) => {
    setComparisonKosts(prev => prev.filter(k => k.id !== kostId))
  }

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <HeroSection onSearch={handleSearch} />

      {/* Featured Kosts Section - Nerdy Style */}
      <section id="kost-listings" className="py-20 bg-background relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute inset-0" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M20 20c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10zm10 0c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10z'/%3E%3C/g%3E%3C/svg%3E")`,
          }} />
        </div>

        <div className="container mx-auto px-4 relative">
          {/* Asymmetric Header */}
          <div className="grid lg:grid-cols-2 gap-12 items-center mb-16">
            <div className="space-y-6">
              <div className="flex items-center gap-3">
                <Badge variant="outline" className="bg-card/80 backdrop-blur-sm border-primary/30 shadow-lg">
                  <TrendingUp className="h-4 w-4 mr-2" />
                  <span className="code-text bg-transparent border-0 p-0">TOP_PICKS</span>
                </Badge>
                <div className="floating-element">
                  <div className="w-2 h-2 bg-primary rounded-full shadow-lg shadow-primary/50"></div>
                </div>
              </div>
              <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight">
                Kost Pilihan
                <br />
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-primary to-accent">
                  Terbaik
                </span>
              </h2>
              <p className="text-muted-foreground text-xl leading-relaxed">
                Temukan kost berkualitas tinggi yang telah dipilih khusus dengan
                <span className="code-text mx-1">AI-powered</span>
                recommendation system
              </p>
            </div>

            {/* Floating Stats */}
            <div className="relative">
              <div className="bg-card/80 backdrop-blur-sm p-6 rounded-2xl border border-primary/20 floating-element shadow-2xl">
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-primary">500+</div>
                    <div className="text-sm text-muted-foreground">Verified Kosts</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-accent">4.9★</div>
                    <div className="text-sm text-muted-foreground">Avg Rating</div>
                  </div>
                  <div className="text-center col-span-2">
                    <div className="text-2xl font-bold text-secondary">10K+</div>
                    <div className="text-sm text-muted-foreground">Happy Users</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Bento Box Grid Layout */}
          <div className="bento-grid mb-12">
            {featuredKosts.map((kost, index) => {
              // Dynamic sizing logic for bento box
              const getBentoClass = (index: number) => {
                switch (index % 6) {
                  case 0: return "bento-item-large"; // First item spans 2 rows
                  case 1: return ""; // Normal size
                  case 2: return "bento-item-wide"; // Wide item spans 2 columns
                  case 3: return ""; // Normal size
                  case 4: return ""; // Normal size
                  case 5: return "bento-item-large"; // Large item spans 2 rows
                  default: return "";
                }
              };

              return (
                <div key={kost.id} className={`${getBentoClass(index)} group`}>
                  <KostCard
                    kost={{
                      ...kost,
                      isWishlisted: wishlistedKosts.includes(kost.id)
                    }}
                    onPreview={handlePreview}
                    onWishlist={handleWishlist}
                    onCompare={handleCompare}
                    isComparing={comparisonKosts.some(k => k.id === kost.id)}
                    className="h-full hover-glow"
                  />
                </div>
              );
            })}
          </div>

          <div className="text-center">
            <Button size="lg" variant="outline">
              Lihat Semua Kost
              <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          </div>
        </div>
      </section>

      <Separator />

      {/* Enhanced Testimonials Section */}
      <section className="py-20 bg-background relative overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-20 left-20 w-64 h-64 bg-primary/20 rounded-full blur-3xl"></div>
          <div className="absolute bottom-20 right-20 w-80 h-80 bg-accent/20 rounded-full blur-3xl"></div>
        </div>

        <div className="container mx-auto px-4 relative">
          {/* Enhanced Header */}
          <div className="text-center mb-16 space-y-6">
            <div className="flex items-center justify-center gap-3">
              <Badge variant="outline" className="bg-card/80 backdrop-blur-sm border-primary/30 shadow-lg">
                <Users className="h-4 w-4 mr-2" />
                <span className="code-text bg-transparent border-0 p-0">USER_FEEDBACK</span>
              </Badge>
              <div className="floating-element">
                <div className="w-2 h-2 bg-accent rounded-full shadow-lg shadow-accent/50"></div>
              </div>
            </div>

            <h2 className="heading-1 heading-gradient">
              What Our Users Say
            </h2>

            <p className="body-large text-muted-foreground max-w-3xl mx-auto">
              Real experiences from
              <span className="code-text mx-1">10,000+</span>
              users who found their perfect kost through our platform
            </p>
          </div>

          {/* Enhanced Testimonials Grid */}
          <div className="grid md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <div
                key={index}
                className="bg-card/80 backdrop-blur-sm p-8 rounded-2xl border border-border/30 hover:border-primary/30 transition-all duration-500 hover:shadow-2xl hover:shadow-primary/20 group"
                style={{animationDelay: `${index * 200}ms`}}
              >
                {/* Rating with Animation */}
                <div className="flex items-center gap-1 mb-6">
                  {Array.from({ length: testimonial.rating }).map((_, i) => (
                    <Star
                      key={i}
                      className="h-5 w-5 fill-yellow-400 text-yellow-400 transition-transform duration-300 group-hover:scale-110"
                      style={{animationDelay: `${i * 100}ms`}}
                    />
                  ))}
                  <span className="ml-2 code-text bg-yellow-400/10 text-yellow-400 border-yellow-400/30">
                    {testimonial.rating}.0
                  </span>
                </div>

                {/* Enhanced Quote */}
                <blockquote className="body-medium text-foreground mb-6 leading-relaxed relative">
                  <span className="text-4xl text-primary/30 absolute -top-2 -left-2 font-serif">"</span>
                  <span className="relative z-10">{testimonial.comment}</span>
                  <span className="text-4xl text-primary/30 absolute -bottom-4 -right-2 font-serif">"</span>
                </blockquote>

                {/* Enhanced User Info */}
                <div className="flex items-center gap-4">
                  <div className="relative w-12 h-12 rounded-full overflow-hidden ring-2 ring-primary/20 group-hover:ring-primary/40 transition-all duration-300">
                    <Image
                      src={testimonial.avatar}
                      alt={`${testimonial.name} avatar`}
                      fill
                      className="object-cover"
                      sizes="48px"
                    />
                  </div>
                  <div className="space-y-1">
                    <div className="font-semibold text-foreground">{testimonial.name}</div>
                    <div className="flex items-center gap-2">
                      <div className="flex items-center gap-1 code-text bg-muted/30 border-border/30">
                        <MapPin className="h-3 w-3 text-primary" />
                        {testimonial.location}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Trust Indicators */}
          <div className="mt-16 text-center">
            <div className="flex items-center justify-center gap-8 flex-wrap">
              <div className="flex items-center gap-2 bg-card/80 backdrop-blur-sm px-4 py-2 rounded-full border border-border/30">
                <Shield className="h-4 w-4 text-accent" />
                <span className="caption">Verified Reviews</span>
              </div>
              <div className="flex items-center gap-2 bg-card/80 backdrop-blur-sm px-4 py-2 rounded-full border border-border/30">
                <Star className="h-4 w-4 text-yellow-400 fill-yellow-400" />
                <span className="caption">4.9/5 Average</span>
              </div>
              <div className="flex items-center gap-2 bg-card/80 backdrop-blur-sm px-4 py-2 rounded-full border border-border/30">
                <Users className="h-4 w-4 text-primary" />
                <span className="caption">10K+ Happy Users</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      <Separator />

      {/* Enhanced CTA Section */}
      <section className="py-20 bg-background relative overflow-hidden">
        {/* Background Effects */}
        <div className="absolute inset-0 hero-gradient opacity-10"></div>
        <div className="absolute inset-0 hero-pattern opacity-5"></div>

        <div className="container mx-auto px-4 text-center relative">
          <div className="max-w-4xl mx-auto space-y-8">
            {/* Enhanced Header */}
            <div className="space-y-6">
              <div className="flex items-center justify-center gap-3">
                <Badge variant="outline" className="bg-card/80 backdrop-blur-sm border-primary/30 shadow-lg">
                  <Shield className="h-4 w-4 mr-2" />
                  <span className="code-text bg-transparent border-0 p-0">JOIN_NOW</span>
                </Badge>
                <div className="floating-element">
                  <div className="w-2 h-2 bg-accent rounded-full shadow-lg shadow-accent/50"></div>
                </div>
              </div>

              <h2 className="heading-1 heading-gradient">
                Ready to Find Your
                <br />
                Perfect Kost?
              </h2>

              <p className="body-large text-muted-foreground max-w-2xl mx-auto">
                Join
                <span className="code-text mx-1">10,000+</span>
                users who have found their ideal living space through our
                <span className="code-text mx-1">AI-powered</span>
                platform
              </p>
            </div>

            {/* Enhanced Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button
                size="lg"
                className="px-8 py-4 font-mono text-sm shadow-lg shadow-primary/30 hover:shadow-accent/30 transition-all duration-300 group"
              >
                <Heart className="h-5 w-5 mr-2 group-hover:scale-110 transition-transform duration-300" />
                START_SEARCH
              </Button>
              <Button
                size="lg"
                variant="outline"
                className="px-8 py-4 font-mono text-sm bg-card/80 backdrop-blur-sm border-border/30 hover:border-primary/50 hover:shadow-lg hover:shadow-primary/20 transition-all duration-300"
              >
                LIST_YOUR_KOST
              </Button>
            </div>

            {/* Trust Indicators */}
            <div className="pt-8">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-3xl mx-auto">
                <div className="bg-card/80 backdrop-blur-sm p-6 rounded-xl border border-border/30 space-y-2">
                  <div className="text-2xl font-bold text-primary font-mono">500+</div>
                  <div className="caption">Verified Kosts</div>
                </div>
                <div className="bg-card/80 backdrop-blur-sm p-6 rounded-xl border border-border/30 space-y-2">
                  <div className="text-2xl font-bold text-accent font-mono">4.9★</div>
                  <div className="caption">User Rating</div>
                </div>
                <div className="bg-card/80 backdrop-blur-sm p-6 rounded-xl border border-border/30 space-y-2">
                  <div className="text-2xl font-bold text-secondary font-mono">24/7</div>
                  <div className="caption">Support</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Comparison Floating Button */}
      {comparisonKosts.length > 0 && (
        <div className="fixed bottom-6 right-6 z-50">
          <Button
            onClick={() => setIsComparisonOpen(true)}
            className="rounded-full shadow-lg"
            size="lg"
          >
            Bandingkan ({comparisonKosts.length})
          </Button>
        </div>
      )}

      {/* Dialogs */}
      <Suspense fallback={<DialogSkeleton />}>
        <KostPreviewDialog
          kost={selectedKost}
          isOpen={isPreviewOpen}
          onClose={() => setIsPreviewOpen(false)}
          onWishlist={handleWishlist}
          onCompare={handleCompare}
          isComparing={selectedKost ? comparisonKosts.some(k => k.id === selectedKost.id) : false}
        />

        <ComparisonDialog
          kosts={comparisonKosts}
          isOpen={isComparisonOpen}
          onClose={() => setIsComparisonOpen(false)}
          onRemoveFromComparison={removeFromComparison}
        />
      </Suspense>
    </div>
  )
}
